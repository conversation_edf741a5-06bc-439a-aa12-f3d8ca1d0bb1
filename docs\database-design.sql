-- 群组记账机器人系统数据库设计
-- 基于ContiNew Admin现有表结构扩展

-- 设置字符集和外键检查
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 群组管理相关表
-- ================================

-- 群组信息表
CREATE TABLE `acc_group` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(100) NOT NULL COMMENT '群组名称',
    `description` varchar(500) DEFAULT NULL COMMENT '群组描述',
    `platform` varchar(20) NOT NULL COMMENT '平台类型(TELEGRAM/DISCORD)',
    `platform_group_id` varchar(100) NOT NULL COMMENT '平台群组ID',
    `owner_id` bigint(20) NOT NULL COMMENT '群主用户ID',
    `subscription_plan` varchar(20) NOT NULL DEFAULT 'TRIAL' COMMENT '订阅套餐(TRIAL/PRO/BUSINESS/ENTERPRISE)',
    `subscription_expires` datetime DEFAULT NULL COMMENT '订阅到期时间',
    `default_currency` varchar(10) NOT NULL DEFAULT 'USD' COMMENT '默认币种',
    `timezone` varchar(50) NOT NULL DEFAULT 'UTC' COMMENT '时区',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用 0:禁用)',
    `settings` json DEFAULT NULL COMMENT '群组设置(JSON格式)',
    `create_user` bigint(20) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform_group` (`platform`, `platform_group_id`),
    KEY `idx_owner_id` (`owner_id`),
    KEY `idx_subscription` (`subscription_plan`, `subscription_expires`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群组信息表';

-- 群组成员表
CREATE TABLE `acc_group_member` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `platform_user_id` varchar(100) NOT NULL COMMENT '平台用户ID',
    `nickname` varchar(100) DEFAULT NULL COMMENT '群内昵称',
    `role` varchar(20) NOT NULL DEFAULT 'MEMBER' COMMENT '角色(OWNER/ACCOUNTANT/MEMBER/AUDITOR)',
    `join_time` datetime NOT NULL COMMENT '加入时间',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:正常 0:已退出)',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_user` (`group_id`, `user_id`),
    KEY `idx_platform_user` (`platform_user_id`),
    KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='群组成员表';

-- ================================
-- 记账核心表
-- ================================

-- 账单表
CREATE TABLE `acc_transaction` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `user_id` bigint(20) NOT NULL COMMENT '记账用户ID',
    `type` varchar(20) NOT NULL COMMENT '类型(INCOME/EXPENSE/TRANSFER)',
    `amount` decimal(15,2) NOT NULL COMMENT '金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `tags` json DEFAULT NULL COMMENT '标签列表',
    `category` varchar(50) DEFAULT NULL COMMENT '分类',
    `transaction_date` datetime NOT NULL COMMENT '交易时间',
    `attachments` json DEFAULT NULL COMMENT '附件列表',
    `split_info` json DEFAULT NULL COMMENT '分摊信息',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:正常 0:已删除)',
    `create_user` bigint(20) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_date` (`group_id`, `transaction_date`),
    KEY `idx_user_date` (`user_id`, `transaction_date`),
    KEY `idx_type_amount` (`type`, `amount`),
    KEY `idx_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单表';

-- 账单修改历史表
CREATE TABLE `acc_transaction_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `transaction_id` bigint(20) NOT NULL COMMENT '账单ID',
    `operation` varchar(20) NOT NULL COMMENT '操作类型(CREATE/UPDATE/DELETE)',
    `old_data` json DEFAULT NULL COMMENT '修改前数据',
    `new_data` json DEFAULT NULL COMMENT '修改后数据',
    `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
    `operation_time` datetime NOT NULL COMMENT '操作时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_transaction_id` (`transaction_id`),
    KEY `idx_operator_time` (`operator_id`, `operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单修改历史表';

-- 分摊记录表
CREATE TABLE `acc_split_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `transaction_id` bigint(20) NOT NULL COMMENT '账单ID',
    `user_id` bigint(20) NOT NULL COMMENT '参与用户ID',
    `amount` decimal(15,2) NOT NULL COMMENT '分摊金额',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态(PENDING/CONFIRMED/PAID)',
    `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_transaction_user` (`transaction_id`, `user_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分摊记录表';

-- ================================
-- 债务管理表
-- ================================

-- 个人债务表
CREATE TABLE `acc_debt` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `creditor_id` bigint(20) NOT NULL COMMENT '债权人ID',
    `debtor_id` bigint(20) NOT NULL COMMENT '债务人ID',
    `amount` decimal(15,2) NOT NULL COMMENT '金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `due_date` datetime DEFAULT NULL COMMENT '到期时间',
    `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态(ACTIVE/PAID/CANCELLED)',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_creditor` (`creditor_id`),
    KEY `idx_debtor` (`debtor_id`),
    KEY `idx_group_status` (`group_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人债务表';

-- 还款记录表
CREATE TABLE `acc_repayment` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `debt_id` bigint(20) NOT NULL COMMENT '债务ID',
    `amount` decimal(15,2) NOT NULL COMMENT '还款金额',
    `repayment_date` datetime NOT NULL COMMENT '还款时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_debt_id` (`debt_id`),
    KEY `idx_repayment_date` (`repayment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='还款记录表';

-- ================================
-- 财务管理表
-- ================================

-- 多币种钱包表
CREATE TABLE `acc_wallet` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `balance` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '余额',
    `frozen_amount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '冻结金额',
    `last_update_time` datetime NOT NULL COMMENT '最后更新时间',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_group_currency` (`group_id`, `currency`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多币种钱包表';

-- 钱包历史记录表
CREATE TABLE `acc_wallet_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `wallet_id` bigint(20) NOT NULL COMMENT '钱包ID',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `operation_type` varchar(20) NOT NULL COMMENT '操作类型(INCOME/EXPENSE/TRANSFER_IN/TRANSFER_OUT/FREEZE/UNFREEZE/RESET)',
    `amount` decimal(15,2) NOT NULL COMMENT '金额',
    `balance_before` decimal(15,2) NOT NULL COMMENT '操作前余额',
    `balance_after` decimal(15,2) NOT NULL COMMENT '操作后余额',
    `frozen_amount_before` decimal(15,2) DEFAULT 0.00 COMMENT '操作前冻结金额',
    `frozen_amount_after` decimal(15,2) DEFAULT 0.00 COMMENT '操作后冻结金额',
    `description` varchar(500) DEFAULT NULL COMMENT '描述',
    `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
    `operate_time` datetime NOT NULL COMMENT '操作时间',
    `business_id` bigint(20) DEFAULT NULL COMMENT '关联业务ID',
    `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型(TRANSACTION/DEBT/TRANSFER/MANUAL)',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `create_user_id` bigint(20) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user_id` bigint(20) DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_currency` (`group_id`, `currency`),
    KEY `idx_wallet_id` (`wallet_id`),
    KEY `idx_operator_time` (`operator_id`, `operate_time`),
    KEY `idx_business` (`business_id`, `business_type`),
    KEY `idx_operate_time` (`operate_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钱包历史记录表';

-- 预算管理表
CREATE TABLE `acc_budget` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '预算名称',
    `category` varchar(50) DEFAULT NULL COMMENT '分类',
    `tags` json DEFAULT NULL COMMENT '标签过滤',
    `amount` decimal(15,2) NOT NULL COMMENT '预算金额',
    `currency` varchar(10) NOT NULL COMMENT '币种',
    `period_type` varchar(20) NOT NULL COMMENT '周期类型(MONTHLY/QUARTERLY/YEARLY)',
    `start_date` date NOT NULL COMMENT '开始日期',
    `end_date` date NOT NULL COMMENT '结束日期',
    `alert_threshold` decimal(5,2) DEFAULT 80.00 COMMENT '预警阈值(%)',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_user` bigint(20) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_period` (`group_id`, `start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算管理表';

-- 定时任务表 (业务层定时任务，存储在 continew_admin 数据库)
CREATE TABLE `acc_scheduled_task` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) NOT NULL COMMENT '群组ID',
    `name` varchar(100) NOT NULL COMMENT '任务名称',
    `type` varchar(20) NOT NULL COMMENT '类型(TRANSACTION/REPORT)',
    `cron_expression` varchar(100) NOT NULL COMMENT 'Cron表达式',
    `task_config` json NOT NULL COMMENT '任务配置',
    `last_run_time` datetime DEFAULT NULL COMMENT '最后执行时间',
    `next_run_time` datetime DEFAULT NULL COMMENT '下次执行时间',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
    `create_user` bigint(20) NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` bigint(20) DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_status` (`group_id`, `status`),
    KEY `idx_next_run` (`next_run_time`),
    KEY `idx_type_status` (`type`, `status`),
    CONSTRAINT `fk_scheduled_task_group` FOREIGN KEY (`group_id`) REFERENCES `acc_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定时任务表';

-- ================================
-- 机器人相关表
-- ================================

-- 机器人配置表
CREATE TABLE `bot_config` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `platform` varchar(20) NOT NULL COMMENT '平台(TELEGRAM/DISCORD)',
    `bot_token` varchar(500) NOT NULL COMMENT 'Bot Token',
    `webhook_url` varchar(500) DEFAULT NULL COMMENT 'Webhook URL',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
    `config_data` json DEFAULT NULL COMMENT '配置数据',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人配置表';

-- 消息记录表
CREATE TABLE `bot_message_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `group_id` bigint(20) DEFAULT NULL COMMENT '群组ID',
    `platform` varchar(20) NOT NULL COMMENT '平台',
    `platform_message_id` varchar(100) NOT NULL COMMENT '平台消息ID',
    `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
    `message_type` varchar(20) NOT NULL COMMENT '消息类型(COMMAND/REPLY/NOTIFICATION)',
    `content` text NOT NULL COMMENT '消息内容',
    `command` varchar(50) DEFAULT NULL COMMENT '命令类型',
    `response` text DEFAULT NULL COMMENT '响应内容',
    `process_status` varchar(20) NOT NULL DEFAULT 'SUCCESS' COMMENT '处理状态',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_group_time` (`group_id`, `create_time`),
    KEY `idx_command` (`command`),
    KEY `idx_platform_msg` (`platform`, `platform_message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息记录表';
